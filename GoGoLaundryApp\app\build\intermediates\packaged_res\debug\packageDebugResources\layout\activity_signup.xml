<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".SignupActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        android:elevation="@dimen/elevation_appbar"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/sign_up"
        app:titleTextColor="@color/white" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!-- Step 1: Basic Information -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/step1View"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="24dp">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/step1Card"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="8dp"
                    app:strokeColor="@color/card_stroke_light"
                    app:strokeWidth="0.5dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="32dp">

                        <TextView
                            android:id="@+id/step1Title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:gravity="center"
                            android:text="Create Account"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
                            android:textColor="@color/text_primary"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="32dp"
                            android:gravity="center"
                            android:text="Step 1: Basic Information"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                            android:textColor="@color/text_secondary" />

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/fullNameLayout"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:hint="@string/full_name"
                            app:boxStrokeColor="@color/primary"
                            app:hintTextColor="@color/primary"
                            app:startIconDrawable="@drawable/ic_person"
                            app:startIconTint="@color/primary">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/fullNameEditText"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="textPersonName" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/phoneLayout"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:hint="@string/phone_number"
                            app:boxStrokeColor="@color/primary"
                            app:hintTextColor="@color/primary"
                            app:startIconDrawable="@drawable/baseline_phone_24"
                            app:startIconTint="@color/primary">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/phoneEditText"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="phone" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/emailLayout"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="24dp"
                            android:hint="@string/email"
                            app:boxStrokeColor="@color/primary"
                            app:hintTextColor="@color/primary"
                            app:startIconDrawable="@drawable/ic_email"
                            app:startIconTint="@color/primary">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/emailEditText"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="textEmailAddress" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/step1NextButton"
                            android:layout_width="match_parent"
                            android:layout_height="56dp"
                            android:layout_marginBottom="16dp"
                            android:text="@string/next"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            app:cornerRadius="12dp" />

                        <TextView
                            android:id="@+id/loginText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:background="?attr/selectableItemBackground"
                            android:clickable="true"
                            android:focusable="true"
                            android:minHeight="48dp"
                            android:padding="16dp"
                            android:text="@string/already_have_account"
                            android:textColor="@color/primary"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Step 2: OTP Verification -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/step2View"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="@dimen/margin_medium"
                android:visibility="gone">

                <TextView
                    android:id="@+id/step2Title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/step_2"
                    android:textColor="@color/text_primary"
                    android:textSize="@dimen/text_size_title"
                    android:textStyle="bold"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/otpInfoText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_medium"
                    android:text="@string/otp_verification"
                    android:textColor="@color/text_secondary"
                    app:layout_constraintTop_toBottomOf="@id/step2Title" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/sendOtpButton"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/button_height"
                    android:layout_marginTop="@dimen/margin_medium"
                    android:text="@string/send_otp"
                    app:cornerRadius="@dimen/button_corner_radius"
                    app:layout_constraintTop_toBottomOf="@id/otpInfoText" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/otpLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_medium"
                    android:hint="@string/enter_otp"
                    app:layout_constraintTop_toBottomOf="@id/sendOtpButton">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/otpEditText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:maxLength="6" />
                </com.google.android.material.textfield.TextInputLayout>

                <TextView
                    android:id="@+id/otpTimerText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_small"
                    android:textColor="@color/text_secondary"
                    android:textSize="@dimen/text_size_small"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/otpLayout"
                    tools:text="Time remaining: 10:00" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/verifyOtpButton"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/button_height"
                    android:layout_marginTop="@dimen/margin_medium"
                    android:text="@string/verify"
                    app:cornerRadius="@dimen/button_corner_radius"
                    app:layout_constraintTop_toBottomOf="@id/otpTimerText" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Step 3: Password and Location -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/step3View"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="@dimen/margin_medium"
                android:visibility="gone">

                <TextView
                    android:id="@+id/step3Title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/step_3"
                    android:textColor="@color/text_primary"
                    android:textSize="@dimen/text_size_title"
                    android:textStyle="bold"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/passwordLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_medium"
                    android:hint="@string/password"
                    app:layout_constraintTop_toBottomOf="@id/step3Title"
                    app:passwordToggleEnabled="true">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/passwordEditText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPassword" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/confirmPasswordLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_medium"
                    android:hint="@string/confirm_password"
                    app:layout_constraintTop_toBottomOf="@id/passwordLayout"
                    app:passwordToggleEnabled="true">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/confirmPasswordEditText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPassword" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/addressLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_medium"
                    android:hint="@string/address"
                    app:layout_constraintTop_toBottomOf="@id/confirmPasswordLayout">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/addressEditText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textMultiLine"
                        android:maxLines="3" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/divisionLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_medium"
                    android:hint="@string/division"
                    app:layout_constraintTop_toBottomOf="@id/addressLayout">

                    <AutoCompleteTextView
                        android:id="@+id/divisionDropdown"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/districtLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_medium"
                    android:hint="@string/district"
                    app:layout_constraintTop_toBottomOf="@id/divisionLayout">

                    <AutoCompleteTextView
                        android:id="@+id/districtDropdown"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/upazillaLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_medium"
                    android:hint="@string/upazilla"
                    app:layout_constraintTop_toBottomOf="@id/districtLayout">

                    <AutoCompleteTextView
                        android:id="@+id/upazillaDropdown"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/signupButton"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/button_height"
                    android:layout_marginTop="@dimen/margin_large"
                    android:text="@string/sign_up"
                    app:cornerRadius="@dimen/button_corner_radius"
                    app:layout_constraintTop_toBottomOf="@id/upazillaLayout" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </FrameLayout>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>
