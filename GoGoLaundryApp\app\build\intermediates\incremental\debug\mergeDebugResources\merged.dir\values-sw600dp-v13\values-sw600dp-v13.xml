<?xml version="1.0" encoding="utf-8"?>
<resources>
    <dimen name="abc_action_bar_content_inset_material">24dp</dimen>
    <dimen name="abc_action_bar_content_inset_with_nav">80dp</dimen>
    <dimen name="abc_action_bar_default_height_material">64dp</dimen>
    <dimen name="abc_action_bar_default_padding_end_material">8dp</dimen>
    <dimen name="abc_action_bar_default_padding_start_material">8dp</dimen>
    <dimen name="abc_config_prefDialogWidth">580dp</dimen>
    <dimen name="abc_text_size_subtitle_material_toolbar">16dp</dimen>
    <dimen name="abc_text_size_title_material_toolbar">20dp</dimen>
    <dimen name="avatar_size">56dp</dimen>
    <dimen name="button_corner_radius">10dp</dimen>
    <dimen name="button_height">64dp</dimen>
    <dimen name="card_corner_radius">10dp</dimen>
    <dimen name="compact_service_card_padding">16dp</dimen>
    <dimen name="compact_service_image_height">140dp</dimen>
    <dimen name="compact_service_image_margin">20dp</dimen>
    <dimen name="design_navigation_max_width">320dp</dimen>
    <dimen name="design_snackbar_action_inline_max_width">0dp</dimen>
    <dimen name="design_snackbar_background_corner_radius">2dp</dimen>
    <dimen name="design_snackbar_extra_spacing_horizontal">24dp</dimen>
    <dimen name="design_snackbar_max_width">576dp</dimen>
    <dimen name="design_snackbar_min_width">320dp</dimen>
    <dimen name="design_snackbar_padding_vertical_2lines">@dimen/design_snackbar_padding_vertical
  </dimen>
    <dimen name="design_tab_scrollable_min_width">160dp</dimen>
    <dimen name="elevation_card_small">1.5dp</dimen>
    <dimen name="grid_spacing">12dp</dimen>
    <dimen name="margin_extra_large">48dp</dimen>
    <dimen name="margin_extra_small">6dp</dimen>
    <dimen name="margin_huge">64dp</dimen>
    <dimen name="margin_large">32dp</dimen>
    <dimen name="margin_medium">24dp</dimen>
    <dimen name="margin_small">12dp</dimen>
    <dimen name="mtrl_bottomappbar_height">64dp</dimen>
    <dimen name="mtrl_toolbar_default_height">64dp</dimen>
    <dimen name="service_card_corner_radius">18dp</dimen>
    <dimen name="service_card_elevation">6dp</dimen>
    <dimen name="service_card_max_height">240dp</dimen>
    <dimen name="service_card_min_height">200dp</dimen>
    <dimen name="service_icon_container_size">90dp</dimen>
    <dimen name="service_icon_size">54dp</dimen>
    <dimen name="text_size_extra_large">24sp</dimen>
    <dimen name="text_size_headline">32sp</dimen>
    <dimen name="text_size_huge">28sp</dimen>
    <dimen name="text_size_large">20sp</dimen>
    <dimen name="text_size_medium">18sp</dimen>
    <dimen name="text_size_micro">14sp</dimen>
    <dimen name="text_size_small">16sp</dimen>
    <dimen name="text_size_title">26sp</dimen>
    <integer name="design_snackbar_text_max_lines">1</integer>
    <integer name="orders_grid_columns">3</integer>
    <style name="Widget.Design.TabLayout" parent="Base.Widget.Design.TabLayout">
    <item name="tabGravity">center</item>
    <item name="tabMode">fixed</item>
  </style>
</resources>